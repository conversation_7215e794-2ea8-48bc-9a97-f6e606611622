<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <div class="flex justify-between">
        <div>
            <flux:heading size="xl" level="1">Order: {{ $order->code }}</flux:heading>
            <flux:subheading size="lg" class="mb-6">Here's the order info and details</flux:subheading>
        </div>
        <div class="flex gap-2">
            <div><flux:badge icon="hashtag" size="sm md:lg">{{ $order->order_code ?? "XX_XXX00-000000" }}</flux:badge></div>
            <div><flux:badge icon="calendar" size="sm md:lg">{{ $order->date->format('d-m-y') }}</flux:badge></div>
            <div><flux:badge icon="{{ $order->status->icon() }}" color="{{ $order->status->color() }}" size="sm md:lg">{{ $order->status->label() }}</flux:badge></div>
        </div>
    </div>

    {{-- Actions --}}
    <div class="flex flex-col my-4">
        <div class="flex justify-between gap-2">
            <div class="flex items-center">
            </div>
            <div>
                @can('view', $order)
                    <flux:button href="{{ route('orders.stats', $order->id) }}" wire:navigate size="sm">Stats</flux:button>
                @endcan
                @can('view', $order)
                    <flux:button wire:click="openCreator({{ $order->id }})" size="sm">Creator</flux:button>
                @endcan
                <flux:dropdown>
                    <flux:button size="sm" icon-trailing="chevron-down">Actions</flux:button>
                    <flux:menu>
                        @can('update', $order)
                            <flux:menu.item wire:click="submit" icon="arrow-turn-down-right" :disabled="$order->status->value !== 'open'">Submit</flux:menu.item>
                            <flux:menu.item wire:click="accept" icon="check" :disabled="$order->status->value !== 'submitted'">Accept</flux:menu.item>
                            <flux:menu.item wire:click="reject" variant="danger" icon="x-mark" :disabled="$order->status->value !== 'submitted'">Reject</flux:menu.item>
                        @endcan

                        @can('view', $order)
                            <flux:menu.separator />
                            <flux:menu.item wire:click="downloadConfirmation" icon="clipboard-document-check" x-bind:disabled="! '{{ $order->confirmation_file }}'">Download confirmation</flux:menu.item>
                        @endcan

                        @can('update', $order)
                            <flux:menu.separator />
                            <flux:menu.item wire:click="edit({{ $order->id }})" icon="pencil-square">Edit</flux:menu.item>
                        @endcan
                        @can('delete', $order)
                            <flux:menu.separator />
                            <flux:menu.item wire:confirm="Are you sure you want to delete this order? This action cannot be undone." wire:click="delete({{ $order->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                        @endcan
                    </flux:menu>
                </flux:dropdown>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {{-- Clients and Partner --}}
        <flux:card size="sm" class="flex flex-col justify-between gap-4">
            <div>
                <flux:subheading class="mb-1">Clients details</flux:subheading>
                <flux:separator class="mb-2"></flux:separator>
            </div>
            <div>
                <flux:heading size="lg" class="mb-1 flex justify-between">
                    <span class="text-sm font-light">Client</span>
                    @can('view', $order->client)
                        <flux:link href="{{ route('clients.show', $order->client->id ?? '') }}" wire:navigate>{{ $order->client->company ?? '-' }}</flux:link>
                    @else
                        <span>{{ $order->client->company ?? '-' }}</span>
                    @endcan
                </flux:heading>
                <flux:heading size="lg" class="mb-1 flex justify-between">
                    <span class="text-sm font-light">Partner</span>
                    @can('view', $order->partner)
                        <flux:link href="{{ route('partners.show', $order->partner->id ?? '') }}" wire:navigate>{{ $order->partner->company ?? '-' }}</flux:link>
                    @else
                        <span>{{ $order->partner->company ?? '-' }}</span>
                    @endcan
                </flux:heading>
                <flux:heading size="lg" class="mb-1 flex justify-between"><span class="text-sm font-light">Int. Referent</span>{{ $order->internalReferent ? $order->internalReferent->first_name . ' ' . $order->internalReferent->last_name : '-' }}</flux:heading>
                <flux:heading size="lg" class="mb-1 flex justify-between"><span class="text-sm font-light">Area Manager</span>{{ $order->areaManager ? $order->areaManager->first_name . ' ' . $order->areaManager->last_name : '-' }}</flux:heading>
            </div>
        </flux:card>

        {{-- Invoicing and Shipping --}}
        <flux:card size="sm" class="flex flex-col justify-between gap-4">
            <div>
                <div>
                    <flux:subheading class="mb-1">Invoicing address</flux:subheading>
                    <flux:separator class="mb-2"></flux:separator>
                </div>
                <flux:link href="#" wire:click="showInvoicingAddress({{ $order->invoicingAddress->id }})">{{ $order->invoicingAddress->company ?? '-' }}</flux:link>
            </div>
            <div>
                <div>
                    <flux:subheading class="mb-1">Shipping address</flux:subheading>
                    <flux:separator class="mb-2"></flux:separator>
                </div>
                <flux:link href="#" wire:click="showShippingAddress({{ $order->shippingAddress->id }})">{{ $order->shippingAddress->company ?? '-' }}</flux:link>
            </div>
        </flux:card>

        {{-- Cart and Summary --}}
            <flux:card size="sm" class="flex flex-col justify-between">
                <div>
                    <flux:subheading class="mb-1">Cart details</flux:subheading>
                    <flux:separator class="mb-2"></flux:separator>
                </div>
                <div>
                    <flux:heading size="lg" class="mt-1 flex justify-between"><span class="text-sm font-light">Amount</span>{{ eu_currency($order->totalAmount) }}</flux:heading>
                    <flux:heading size="lg" class="mt-1 flex justify-between"><span class="text-sm font-light">With addons</span>{{ eu_currency($order->getTotalAmountAttribute($withAddons = true)) }}</flux:heading>
                </div>
                <div>
                    <flux:heading size="lg" class="mb-1 flex justify-end">{{ $order->paymentTerm ? $order->paymentTerm->name : '-' }}</flux:heading>
                </div>
            </flux:card>
    </div>

    {{-- Callout Messages --}}
    @if ($callout->show)
        <flux:callout variant="{{ $callout->variant }}" icon="{{ $callout->icon }}" heading="{{ $callout->heading }}" class="mt-4" />
    @endif

    <flux:separator variant="subtle" class="mt-8 mb-12" />

    {{-- Order Rows Listing --}}
    <livewire:components.inner-table.order-rows :resourceType="class_basename($order)" :resourceValue="$order" />

    <flux:separator variant="subtle" class="my-12" />

    {{-- Collaborators Listing --}}
    <livewire:components.inner-table.collaborators :resourceType="class_basename($order)" :resourceValue="$order" />

    <flux:separator variant="subtle" class="my-12" />

    {{-- Assets Listing --}}
    <livewire:components.inner-table.assets :resourceType="class_basename($order)" :resourceValue="$order" />

    <flux:separator variant="subtle" class="my-12" />

    {{-- Users Listing --}}
    <livewire:components.inner-table.users :resourceType="class_basename($order)" :resourceValue="$order" />

    {{-- View Invoicing Address - Modal --}}
    <flux:modal name="view-invoicing-address" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">View address</flux:heading>
            <flux:subheading>View address for the order.</flux:subheading>
        </div>

        <div class="flex flex-col gap-6">
            <flux:input wire:model="invoicingAddress.name" readonly type="text" placeholder="HeadQuarter" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="invoicingAddress.code_invoicing" readonly type="text" placeholder="*********" variant="filled" label="Code" description="This will be publicly displayed." badge="Optional" />
            <flux:input wire:model="invoicingAddress.company" readonly type="text" placeholder="Green SRL" variant="filled" label="Company" description="This will be publicly displayed." />
            <flux:input wire:model="invoicingAddress.vat_number" readonly type="text" placeholder="VAT Number" variant="filled" label="VAT Number" badge="Optional" description="This will be publicly displayed." />
            <flux:input wire:model="invoicingAddress.fiscal_code" readonly type="text" placeholder="Fiscal Code" variant="filled" label="Fiscal Code" badge="Optional" description="This will be publicly displayed." />
            <flux:input wire:model="invoicingAddress.sdi_code" readonly type="text" placeholder="SDI Code" variant="filled" label="SDI Code" badge="Optional" description="This will be publicly displayed." />
            <flux:input wire:model="invoicingAddress.street" readonly type="text" placeholder="Corso Venezia, 11" variant="filled" label="Street" description="This will be publicly displayed." badge="Optional" />
            <flux:input wire:model="invoicingAddress.city" readonly type="text" placeholder="Milan" variant="filled" label="City" description="This will be publicly displayed." badge="Optional" />
            <flux:input wire:model="invoicingAddress.state" readonly type="text" placeholder="Milan" variant="filled" label="State" description="This will be publicly displayed." badge="Optional" />
            <flux:input wire:model="invoicingAddress.zip" readonly type="text" placeholder="20122" variant="filled" label="ZIP" description="This will be publicly displayed." badge="Optional" />
            <flux:select searchable wire:model="invoicingAddress.country" disabled variant="listbox" searchable placeholder="Choose the country..." label="Country" description="This will be publicly displayed." badge="Optional">
                @foreach($countries as $country)
                    <flux:select.option value="{{ $country->value }}">{{ str_replace('_', ' ', $country->name) }}</flux:select.option>
                @endforeach
            </flux:select>
        </div>
    </flux:modal>

    {{-- View Shipping Address - Modal --}}
    <flux:modal name="view-shipping-address" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">View address</flux:heading>
            <flux:subheading>View address for the order.</flux:subheading>
        </div>

        <div class="flex flex-col gap-6">
            <flux:input wire:model="shippingAddress.name" readonly type="text" placeholder="HeadQuarter" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="shippingAddress.code_shipping" readonly type="text" placeholder="*********" variant="filled" label="Code" description="This will be publicly displayed." badge="Optional" />
            <flux:input wire:model="shippingAddress.company" readonly type="text" placeholder="Green SRL" variant="filled" label="Company/Description" description="This will be publicly displayed." />
            <flux:input wire:model="shippingAddress.street" readonly type="text" placeholder="Corso Venezia, 11" variant="filled" label="Street" description="This will be publicly displayed." badge="Optional" />
            <flux:input wire:model="shippingAddress.city" readonly type="text" placeholder="Milan" variant="filled" label="City" description="This will be publicly displayed." badge="Optional" />
            <flux:input wire:model="shippingAddress.state" readonly type="text" placeholder="Milan" variant="filled" label="State" description="This will be publicly displayed." badge="Optional" />
            <flux:input wire:model="shippingAddress.zip" readonly type="text" placeholder="20122" variant="filled" label="ZIP" description="This will be publicly displayed." badge="Optional" />
            <flux:select searchable wire:model="shippingAddress.country" disabled variant="listbox" searchable placeholder="Choose the country..." label="Country" description="This will be publicly displayed." badge="Optional">
                @foreach($countries as $country)
                    <flux:select.option value="{{ $country->value }}">{{ str_replace('_', ' ', $country->name) }}</flux:select.option>
                @endforeach
            </flux:select>
        </div>
    </flux:modal>

</flux:main>

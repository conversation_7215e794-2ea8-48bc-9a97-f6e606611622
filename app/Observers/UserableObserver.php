<?php

namespace App\Observers;

use App\Models\Client;
use App\Models\Order\Order;
use App\Models\Partner;
use App\Models\Project\Project;
use Illuminate\Database\Eloquent\Model;

class UserableObserver
{

    /**
     * @param Model $model
     * @return void
     * This method is called when a model is created.
     */
    public function created(Model $model)
    {
        $userId = auth()->id();
        if ($userId && method_exists($model, 'users')) {
            $model->users()->attach($userId);
        }

        // If the model is a Client with a Partner
        if ($model instanceof Client) {
            if($model->partner_id){
                // Attach all users from the Partner to the Client
                $this->attachPartnerUsersTo($model);
            }
            if ($model->users()->exists()) {
                // Attach all users of the Client to the associated Orders and Projects
                $attachedUserIds = $model->users()->pluck('id')->toArray();
                $this->attachUsersToClientOrdersAndProjects($attachedUserIds, $model);
            }
        } else if($model instanceof Order || $model instanceof Project){
            if($model->client_id) {
                $this->attachClientUsersTo($model);
            }
            if($model->partner_id) {
                $this->attachPartnerUsersTo($model);
            }
        } else if($model instanceof Partner){
            if ($model->users()->exists()) {
                // If the model is a Partner, attach all users to Order and Project models associated with the associated Clients
                $attachedUserIds = $model->users()->pluck('id')->toArray();
                $associatedClients = $model->clients()->get();

                foreach ($associatedClients as $client) {
                    $this->attachUsersTo($attachedUserIds, $client);

                    $this->attachUsersToClientOrdersAndProjects($attachedUserIds, $client);
                }
            }
        }
    }

    public function attachClientUsersTo(Model $model){
        $userId = auth()->id();
        $client = $model->client()->with('users')->first();

        if ($client && $client->users->isNotEmpty()) {
            // Get the current user's ID and exclude it from the list of users to attach
            $clientUserIds = $client->users->pluck('id')->toArray();
            $clientUserIds = array_diff($clientUserIds, [$userId]);

            if (!empty($clientUserIds)) {
                $this->attachUsersTo($clientUserIds, $model);
            }
        }
    }

    public function attachPartnerUsersTo(Model $model){
        $userId = auth()->id();
        $partner = $model->partner()->with('users')->first();

        if ($partner && $partner->users->isNotEmpty()) {
            // Get the current user's ID and exclude it from the list of users to attach
            $partnerUserIds = $partner->users->pluck('id')->toArray();
            $partnerUserIds = array_diff($partnerUserIds, [$userId]);

            if (!empty($partnerUserIds)) {
                $this->attachUsersTo($partnerUserIds, $model);
            }
        }
    }

    public function attachUsersTo($usersIds, Model $model){
        // Attach users to the model, excluding the users already attached
        $existingUserIds = $model->users()->pluck('users.id')->toArray();
        $usersToAttach = array_diff($usersIds, $existingUserIds);

        if (!empty($usersToAttach)) {
            $model->users()->attach($usersToAttach, ['relation_type' => 'relational']);
        }
    }

    public function attachUsersToClientOrdersAndProjects($attachedUserIds, Client $client){
        // Attach users to the Client associated Orders and Projects
        $clientOrders = $client->orders()->get();
        foreach ($clientOrders as $order) {
            $this->attachUsersTo($attachedUserIds, $order);
        }
        $clientProjects = $client->projects()->get();
        foreach ($clientProjects as $project) {
            $this->attachUsersTo($attachedUserIds, $project);
        }
    }

    /**
     * @param Model $model
     * @return void
     * This method is called when a model is updated.
     */
    public function updated(Model $model){
        if ($model instanceof Client) {
            $model->users()->wherePivot('relation_type', 'relational')->detach();
            if($model->partner_id){
                $this->attachPartnerUsersTo($model);
            }
            $this->detachUsersFromClientOrdersAndProjects($model);
            if ($model->users()->exists()) {
                // Attach all users of the Client to the associated Orders and Projects
                $attachedUserIds = $model->users()->pluck('id')->toArray();
                $this->attachUsersToClientOrdersAndProjects($attachedUserIds, $model);
            }
        } else if($model instanceof Order || $model instanceof Project){
            $model->users()->wherePivot('relation_type', 'relational')->detach();

            if($model->client_id) {
                $this->attachClientUsersTo($model);
            }
            if($model->partner_id) {
                $this->attachPartnerUsersTo($model);
            }
        } else if($model instanceof Partner){
            // TODO DELETE ALL USERS FROM ORDERS AND PROJECTS ASSOCIATED WITH THE CLIENT OF THE PARTNER
            if ($model->users()->exists()) {
                // If the model is a Partner, attach all users to Order and Project models associated with the associated Clients
                $attachedUserIds = $model->users()->pluck('id')->toArray();
                $associatedClients = $model->clients()->get();

                foreach ($associatedClients as $client) {
                    $this->attachUsersTo($attachedUserIds, $client);

                    $this->attachUsersToClientOrdersAndProjects($attachedUserIds, $client);
                }
            }
        }
    }

    public function detachUsersFromClientOrdersAndProjects(Client $client){
        // Detach users from the Client associated Orders and Projects
        $clientOrders = $client->orders()->get();
        foreach ($clientOrders as $order) {
            $order->users()->wherePivot('relation_type', 'relational')->detach();
        }
        $clientProjects = $client->projects()->get();
        foreach ($clientProjects as $project) {
            $project->users()->wherePivot('relation_type', 'relational')->detach();
        }
    }

    /**
     * @param Model $model
     * @return void
     * Detach all users when the model is being deleted.
     */
    public function deleting(Model $model)
    {
        if (method_exists($model, 'users')) {
            $model->users()->detach();
        }
    }
}

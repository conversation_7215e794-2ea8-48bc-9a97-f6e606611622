<?php

namespace App\Enums;

enum AddonCodes: string
{
    case AFF = 'AFF';
    case ARG = 'ARG';
    case BANK_FEE = 'BANK_FEE';
    case CRATE = 'CRATE';
    case CUSTOM = 'CUSTOM';
    case DEBOSSING = 'DEBOSSING';
    case HP = 'HP';
    case INST = 'INST';
    case INSURANCE = 'INSURANCE';
    case LAUNDRY = 'LAUNDRY';
    case LOGO = 'LOGO';
    case POLISHING = 'POLISHING';
    case PROJECT_FEES = 'PROJECT_FEES';
    case REFURBISHMENT = 'REFURBISHMENT';
    case RICAMO = 'RICAMO';
    case STAGNA = 'STAGNA';
    case TRA = 'TRA';

    /**
     * Get the description for the addon code.
     */
    public function label(): string
    {
        return match($this) {
            self::AFF => 'SHARPENING',
            self::ARG => 'SILVER PLATING',
            self::BANK_FEE => 'BANK COST_CLIENT',
            self::CRATE => 'CRATE',
            self::CUSTOM => 'CUSTOM DUTIES AND TAXES',
            self::DEBOSSING => 'DEBOSSING',
            self::HP => 'HANDLING AND PACKING',
            self::INST => 'INSTALLATION AND OPERATION CHECK',
            self::INSURANCE => 'INSURANCE ON THE GOODS',
            self::LAUNDRY => 'LAUNDRY SERVICE',
            self::LOGO => 'LOGO',
            self::POLISHING => 'CUTLERY POLISHING',
            self::PROJECT_FEES => 'PROJECT FEES',
            self::REFURBISHMENT => 'CUTLERY REFURBISHMENT',
            self::RICAMO => 'EMBROIDERY',
            self::STAGNA => 'STAGNATURA',
            self::TRA => 'SHIPPING COST',
        };
    }
}
